import React, { useState, useEffect } from 'react';
import { Puck } from '@measured/puck';
import '@measured/puck/puck.css';
import './PuckEditor.css';
import { useNavigate } from 'react-router-dom';
import { Contain<PERSON>, <PERSON><PERSON>, Spin<PERSON>, <PERSON><PERSON> } from 'react-bootstrap';
import authService from '../../services/authService';
import { suppressResizeObserverErrors } from '../../utils/errorHandlers';
import { puckConfig } from './puckConfig';

interface PuckEditorProps {
  className?: string;
}

const PuckEditor: React.FC<PuckEditorProps> = ({ className }) => {
  const navigate = useNavigate();
  const searchParams = new URLSearchParams(window.location.search);
  const pageSlug = searchParams.get('page');

  const [puckData, setPuckData] = useState<any>({
    content: [],
    root: {
      title: 'New Page',
      metaDescription: '',
    },
  });
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string>('');
  const [isAdmin, setIsAdmin] = useState(false);
  const [pageTitle, setPageTitle] = useState<string>('New Page');

  useEffect(() => {
    checkAdminStatus();
  }, []);

  useEffect(() => {
    if (isAdmin && pageSlug) {
      loadPageData();
    }
  }, [isAdmin, pageSlug]);

  const checkAdminStatus = async () => {
    try {
      setLoading(true);
      setError('');

      // Check if user is admin
      const adminStatus = await authService.checkAdminStatus();
      if (!adminStatus.is_admin) {
        setError('You must be an administrator to use the visual editor.');
        setLoading(false);
        return;
      }
      setIsAdmin(true);
      
      // If no specific page is requested, finish loading
      if (!pageSlug) {
        setLoading(false);
      }
    } catch (err: any) {
      console.error('Error checking admin status:', err);
      setError(err.message || 'Failed to verify admin status');
      setLoading(false);
    }
  };

  const loadPageData = async () => {
    if (!pageSlug) return;

    try {
      setLoading(true);
      const token = localStorage.getItem('auth_token');
      const response = await fetch(`${process.env.REACT_APP_API_URL}/admin/pages/${pageSlug}/puck-data`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error('Failed to load page data');
      }

      const result = await response.json();
      if (result.success) {
        setPuckData(result.data.content);
        setPageTitle(result.data.title);
      } else {
        setError(result.message || 'Failed to load page data');
      }
    } catch (error) {
      console.error('Error loading page data:', error);
      setError('Failed to load page data');
    } finally {
      setLoading(false);
    }
  };

  const handleSave = async (data: any) => {
    try {
      setSaving(true);
      setError('');

      if (pageSlug) {
        // Save to backend for specific page
        const token = localStorage.getItem('auth_token');
        const response = await fetch(`${process.env.REACT_APP_API_URL}/admin/pages/${pageSlug}/puck-data`, {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            content: data,
            title: data.root?.title || pageTitle,
            meta_description: data.root?.metaDescription || '',
          }),
        });

        if (!response.ok) {
          throw new Error('Failed to save page');
        }

        const result = await response.json();
        if (!result.success) {
          throw new Error(result.message || 'Failed to save page');
        }

        alert('Page saved successfully!');
      } else {
        // For now, just save to localStorage as a demo for general editor
        localStorage.setItem('puck-editor-data', JSON.stringify(data));
        alert('Design saved successfully! (Demo mode - saved to localStorage)');
      }

      // Update the current data
      setPuckData(data);
    } catch (err: any) {
      console.error('Error saving:', err);
      setError(err.message || 'Failed to save design');
    } finally {
      setSaving(false);
    }
  };

  const handleExit = () => {
    if (window.confirm('Are you sure you want to exit? Any unsaved changes will be lost.')) {
      navigate('/');
    }
  };

  // Suppress ResizeObserver errors
  useEffect(() => {
    const cleanup = suppressResizeObserverErrors();
    return cleanup;
  }, []);

  if (loading) {
    return (
      <Container className="py-5 text-center">
        <Spinner animation="border" role="status">
          <span className="visually-hidden">Loading...</span>
        </Spinner>
        <p className="mt-3">Loading visual editor...</p>
      </Container>
    );
  }

  if (error) {
    return (
      <Container className="py-5">
        <Alert variant="danger">
          <Alert.Heading>Error</Alert.Heading>
          <p>{error}</p>
          <hr />
          <div className="d-flex justify-content-end">
            <Button variant="outline-danger" onClick={() => navigate('/')}>
              Go Back
            </Button>
          </div>
        </Alert>
      </Container>
    );
  }

  return (
    <div className={`puck-editor-container ${className || ''}`}>
      <Puck
        config={puckConfig}
        data={puckData}
        onPublish={handleSave}
        headerTitle={pageSlug ? `Visual Editor - ${pageTitle}` : "Visual Editor"}
        headerPath=""
        overrides={{
          header: ({ children, ...props }) => (
            <div {...props} style={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'space-between',
              padding: '8px 16px',
              borderBottom: '1px solid var(--puck-color-grey-09, #dcdcdc)',
              backgroundColor: 'var(--puck-color-white, #ffffff)',
              minHeight: '56px',
              boxSizing: 'border-box',
              fontFamily: 'var(--puck-font-family, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif)',
            }}>
              {/* Left side - Navigation icons and title */}
              <div style={{
                display: 'flex',
                alignItems: 'center',
                gap: '12px',
                flex: '1',
                minWidth: '0'
              }}>
                {children}
              </div>

              {/* Right side - Action buttons */}
              <div style={{
                display: 'flex',
                alignItems: 'center',
                gap: '8px',
                flexShrink: '0'
              }}>
                {saving && (
                  <span style={{
                    fontSize: '14px',
                    color: 'var(--puck-color-grey-05, #767676)',
                    display: 'flex',
                    alignItems: 'center',
                    gap: '8px',
                    whiteSpace: 'nowrap'
                  }}>
                    <Spinner animation="border" size="sm" />
                    Saving...
                  </span>
                )}

                {/* View page button */}
                {pageSlug && (
                  <Button
                    variant="outline-secondary"
                    size="sm"
                    onClick={() => window.open(`/${pageSlug}`, '_blank')}
                    disabled={saving}
                    style={{
                      fontSize: '14px',
                      padding: '6px 12px',
                      borderRadius: '6px',
                      border: '1px solid var(--puck-color-grey-08, #c3c3c3)',
                      backgroundColor: 'transparent',
                      color: 'var(--puck-color-grey-04, #5a5a5a)',
                      fontFamily: 'var(--puck-font-family, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif)',
                      fontWeight: '500',
                      cursor: 'pointer',
                      transition: 'all 0.15s ease-in-out'
                    }}
                  >
                    View page
                  </Button>
                )}

                {/* Exit button */}
                <Button
                  variant="outline-secondary"
                  size="sm"
                  onClick={handleExit}
                  disabled={saving}
                  style={{
                    fontSize: '14px',
                    padding: '6px 12px',
                    borderRadius: '6px',
                    border: '1px solid var(--puck-color-grey-08, #c3c3c3)',
                    backgroundColor: 'transparent',
                    color: 'var(--puck-color-grey-04, #5a5a5a)',
                    fontFamily: 'var(--puck-font-family, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif)',
                    fontWeight: '500',
                    cursor: 'pointer',
                    transition: 'all 0.15s ease-in-out'
                  }}
                >
                  Exit
                </Button>
              </div>
            </div>
          ),
        }}
      />
    </div>
  );
};

export default PuckEditor;
