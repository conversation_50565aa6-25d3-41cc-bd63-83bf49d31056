<?php

namespace Database\Seeders;

use App\Models\Page;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class StaticPagesSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $pages = [
            [
                'title' => 'About Us',
                'slug' => 'about',
                'content' => [
                    'content' => [
                        [
                            'type' => 'Text',
                            'props' => [
                                'content' => '<h1>About Us</h1><h2>Our Story</h2><p>Welcome to our company! We are dedicated to providing excellent printing and document management solutions.</p><p>Our team is committed to delivering high-quality services that meet your business needs.</p>',
                                'textAlign' => 'left',
                                'fontSize' => '16px',
                                'lineHeight' => '1.6',
                                'color' => '#333333',
                                'marginTop' => '0px',
                                'marginBottom' => '20px',
                            ]
                        ]
                    ],
                    'root' => [
                        'title' => 'About Us',
                        'metaDescription' => 'Learn more about our company, mission, and values.',
                    ]
                ],
                'meta_title' => 'About Us - Learn About Our Company',
                'meta_description' => 'Learn more about our company, mission, and values. Discover what makes us unique.',
                'status' => 'published',
                'published_at' => now(),
                'sort_order' => 1,
            ],
            [
                'title' => 'Contact Us',
                'slug' => 'contact',
                'content' => [
                    'content' => [
                        [
                            'type' => 'Text',
                            'props' => [
                                'content' => '<h1>Contact Us</h1><h2>Get In Touch</h2><p>We would love to hear from you! Contact us using the information below:</p><ul><li><strong>Email:</strong> <EMAIL></li><li><strong>Phone:</strong> +****************</li><li><strong>Address:</strong> 123 Business Street, City, State 12345</li></ul>',
                                'textAlign' => 'left',
                                'fontSize' => '16px',
                                'lineHeight' => '1.6',
                                'color' => '#333333',
                                'marginTop' => '0px',
                                'marginBottom' => '20px',
                            ]
                        ]
                    ],
                    'root' => [
                        'title' => 'Contact Us',
                        'metaDescription' => 'Contact us for any questions or inquiries.',
                    ]
                ],
                'meta_title' => 'Contact Us - Get In Touch',
                'meta_description' => 'Contact us for any questions or inquiries. We are here to help you.',
                'status' => 'published',
                'published_at' => now(),
                'sort_order' => 2,
            ],
            [
                'title' => 'Frequently Asked Questions',
                'slug' => 'faq',
                'content' => [
                    'content' => [
                        [
                            'type' => 'Text',
                            'props' => [
                                'content' => '<h1>FAQ</h1><h2>Common Questions</h2><h3>How do I place an order?</h3><p>You can place an order by logging into your account and selecting the printing services you need.</p><h3>What file formats do you accept?</h3><p>We accept PDF, DOC, DOCX, and most common image formats.</p><h3>How long does printing take?</h3><p>Standard printing orders are completed within 1-3 business days.</p>',
                                'textAlign' => 'left',
                                'fontSize' => '16px',
                                'lineHeight' => '1.6',
                                'color' => '#333333',
                                'marginTop' => '0px',
                                'marginBottom' => '20px',
                            ]
                        ]
                    ],
                    'root' => [
                        'title' => 'FAQ',
                        'metaDescription' => 'Find answers to frequently asked questions.',
                    ]
                ],
                'meta_title' => 'FAQ - Frequently Asked Questions',
                'meta_description' => 'Find answers to frequently asked questions about our services.',
                'status' => 'published',
                'published_at' => now(),
                'sort_order' => 3,
            ],
        ];

        foreach ($pages as $pageData) {
            Page::updateOrCreate(
                ['slug' => $pageData['slug']],
                $pageData
            );
        }
    }
}
