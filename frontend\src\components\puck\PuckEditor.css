/* PuckEditor CSS fixes and enhancements */

/* Prevent ResizeObserver issues by stabilizing layout */
.puck-editor-container {
  position: relative;
  width: 100%;
  height: 100vh;
  overflow: hidden;
}

/* Stabilize Puck component dimensions */
.puck-root {
  contain: layout style;
  will-change: auto;
}

/* Prevent layout thrashing in component panels */
.puck-component-list,
.puck-fields {
  contain: layout;
  transform: translateZ(0); /* Force GPU acceleration */
}

/* Smooth transitions for component interactions */
.puck-component {
  transition: transform 0.2s ease-out, box-shadow 0.2s ease-out;
  will-change: transform;
}

.puck-component:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* Prevent text selection issues during drag operations */
.puck-dragging {
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}

/* Stabilize iframe dimensions for preview */
.puck-preview iframe {
  width: 100% !important;
  height: 100% !important;
  border: none;
  contain: strict;
}

/* Fix for component field inputs */
.puck-field input,
.puck-field textarea,
.puck-field select {
  width: 100%;
  box-sizing: border-box;
  transition: border-color 0.2s ease;
}

.puck-field input:focus,
.puck-field textarea:focus,
.puck-field select:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

/* Component-specific styles */
.text-component {
  word-wrap: break-word;
  overflow-wrap: break-word;
}

.hero-component {
  position: relative;
  overflow: hidden;
}

.hero-component::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: inherit;
  z-index: -1;
}

.button-component {
  display: inline-block;
  transition: all 0.2s ease-in-out;
}

.button-component:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.card-component {
  transition: box-shadow 0.2s ease;
}

.card-component:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* Responsive design helpers */
@media (max-width: 768px) {
  .puck-editor-container {
    height: 100vh;
  }
  
  .puck-component {
    margin-bottom: 1rem;
  }
}

/* Loading states */
.puck-loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
  color: #666;
}

.puck-loading::after {
  content: '';
  width: 20px;
  height: 20px;
  border: 2px solid #f3f3f3;
  border-top: 2px solid #007bff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-left: 10px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Error states */
.puck-error {
  padding: 1rem;
  background-color: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
  border-radius: 0.375rem;
  margin: 1rem 0;
}

/* Success states */
.puck-success {
  padding: 1rem;
  background-color: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
  border-radius: 0.375rem;
  margin: 1rem 0;
}

/* Accessibility improvements */
.puck-component:focus {
  outline: 2px solid #007bff;
  outline-offset: 2px;
}

.puck-component[aria-selected="true"] {
  box-shadow: 0 0 0 2px #007bff;
}

/* Performance optimizations */
.puck-viewport {
  contain: layout style paint;
  transform: translateZ(0);
}

/* Prevent memory leaks from animations */
.puck-component.puck-removing {
  animation: fadeOut 0.3s ease-out forwards;
}

@keyframes fadeOut {
  from { opacity: 1; transform: scale(1); }
  to { opacity: 0; transform: scale(0.95); }
}

/* Ensure header actions are visible and properly styled */
.puck-header-actions {
  display: flex !important;
  gap: 0.5rem;
  align-items: center;
}

/* Button component styling to match Puck's design system */
.puck-button-container {
  font-family: var(--puck-font-family, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif);
}

/* Override DattaAble button styles within Puck editor */
.puck-editor-container .puck-button-container button,
.puck-editor-container .puck-button-container a {
  font-family: var(--puck-font-family, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif) !important;
  border-radius: 6px !important;
  font-weight: 500 !important;
  transition: all 0.15s ease-in-out !important;
  box-sizing: border-box !important;
}

/* Ensure buttons don't inherit DattaAble dark theme colors */
.puck-editor-container .puck-button-container button:not(:disabled):hover,
.puck-editor-container .puck-button-container a:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
}

/* Focus states for accessibility */
.puck-editor-container .puck-button-container button:focus,
.puck-editor-container .puck-button-container a:focus {
  outline: 2px solid #0158ad !important;
  outline-offset: 2px !important;
}

/* Disabled state styling */
.puck-editor-container .puck-button-container button:disabled {
  cursor: not-allowed !important;
  opacity: 0.6 !important;
  transform: none !important;
  box-shadow: none !important;
}

/* Ensure proper spacing in the editor preview */
.puck-editor-container [data-puck-component] .puck-button-container {
  margin: 16px 0;
}

/* Component list styling improvements */
.puck-component-list {
  font-family: var(--puck-font-family, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif);
}

/* Ensure component labels are readable */
.puck-component-list .puck-component-label {
  font-size: 14px;
  font-weight: 500;
  color: var(--puck-color-grey-04, #5a5a5a);
}

/* Component drag handles */
.puck-component-list .puck-component-item {
  border-radius: 6px;
  transition: all 0.15s ease-in-out;
  border: 1px solid var(--puck-color-grey-09, #dcdcdc);
}

.puck-component-list .puck-component-item:hover {
  border-color: var(--puck-color-azure-04, #0158ad);
  box-shadow: 0 1px 3px rgba(1, 88, 173, 0.1);
}

/* Override DattaAble global button styles specifically for Puck editor */
.puck-editor-container .btn,
.puck-editor-container button {
  background-color: initial !important;
  color: initial !important;
  border: initial !important;
  font-family: var(--puck-font-family, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif) !important;
}

/* Ensure Puck's own buttons maintain their styling */
.puck-editor-container .puck-button-container .btn,
.puck-editor-container .puck-button-container button,
.puck-editor-container .puck-button-container a {
  background-color: var(--button-bg) !important;
  color: var(--button-color) !important;
  border: var(--button-border) !important;
}

/* Prevent DattaAble theme from affecting Puck editor interface */
.puck-editor-container {
  --button-bg: #0158ad;
  --button-color: #ffffff;
  --button-border: 1px solid #0158ad;
}

/* Ensure proper isolation of Puck editor from parent theme */
.puck-editor-container * {
  box-sizing: border-box;
}

/* Override any inherited dark theme colors for buttons */
.puck-editor-container .puck-button-container button,
.puck-editor-container .puck-button-container a {
  background-color: var(--button-bg) !important;
  color: var(--button-color) !important;
  border: var(--button-border) !important;
}

/* Specific overrides for different button variants */
.puck-editor-container .puck-button-container[data-variant="secondary"] {
  --button-bg: #5a5a5a;
  --button-color: #ffffff;
  --button-border: 1px solid #5a5a5a;
}

.puck-editor-container .puck-button-container[data-variant="outline"] {
  --button-bg: transparent;
  --button-color: #0158ad;
  --button-border: 1px solid #0158ad;
}

.puck-editor-container .puck-button-container[data-variant="ghost"] {
  --button-bg: transparent;
  --button-color: #0158ad;
  --button-border: 1px solid transparent;
}

/* Puck Editor Header Styling */
.puck-editor-container [data-puck-header] {
  font-family: var(--puck-font-family, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif) !important;
}

/* Header title styling */
.puck-editor-container [data-puck-header] h1,
.puck-editor-container [data-puck-header] .puck-header-title {
  font-size: 16px !important;
  font-weight: 600 !important;
  color: var(--puck-color-grey-01, #181818) !important;
  margin: 0 !important;
  line-height: 1.4 !important;
}

/* Header button group alignment */
.puck-editor-container [data-puck-header] .puck-header-actions,
.puck-editor-container [data-puck-header] [data-puck-actions] {
  display: flex !important;
  align-items: center !important;
  gap: 8px !important;
  height: 40px !important;
}

/* Ensure proper icon arrangement in header */
.puck-editor-container [data-puck-header] {
  display: flex !important;
  align-items: center !important;
  justify-content: space-between !important;
}

/* Left side navigation icons (undo/redo, etc.) */
.puck-editor-container [data-puck-header] > div:first-child {
  display: flex !important;
  align-items: center !important;
  gap: 12px !important;
  flex: 1 !important;
  min-width: 0 !important;
}

/* Right side action buttons */
.puck-editor-container [data-puck-header] > div:last-child {
  display: flex !important;
  align-items: center !important;
  gap: 8px !important;
  flex-shrink: 0 !important;
}

/* Individual header buttons */
.puck-editor-container [data-puck-header] button,
.puck-editor-container [data-puck-header] .btn {
  font-size: 14px !important;
  padding: 6px 12px !important;
  border-radius: 6px !important;
  font-family: var(--puck-font-family, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif) !important;
  font-weight: 500 !important;
  line-height: 1.4 !important;
  height: 32px !important;
  display: inline-flex !important;
  align-items: center !important;
  justify-content: center !important;
  white-space: nowrap !important;
  transition: all 0.15s ease-in-out !important;
  cursor: pointer !important;
  border: 1px solid var(--puck-color-grey-08, #c3c3c3) !important;
  background-color: var(--puck-color-white, #ffffff) !important;
  color: var(--puck-color-grey-04, #5a5a5a) !important;
}

/* Primary action button (Publish) */
.puck-editor-container [data-puck-header] button[data-puck-primary],
.puck-editor-container [data-puck-header] .btn-primary,
.puck-editor-container button[data-puck-button-primary],
.puck-editor-container button:has(svg + :contains("Publish")) {
  background-color: #007bff !important;
  border-color: #007bff !important;
  color: #ffffff !important;
  font-weight: 500 !important;
}

/* Ensure Puck's built-in Publish button styling */
.puck-editor-container [data-puck-actions] button:last-child,
.puck-editor-container button:contains("Publish") {
  background-color: #007bff !important;
  border-color: #007bff !important;
  color: #ffffff !important;
}

/* Button hover states */
.puck-editor-container [data-puck-header] button:hover,
.puck-editor-container [data-puck-header] .btn:hover {
  background-color: var(--puck-color-grey-11, #f5f5f5) !important;
  border-color: var(--puck-color-grey-07, #ababab) !important;
}

.puck-editor-container [data-puck-header] button[data-puck-primary]:hover,
.puck-editor-container [data-puck-header] .btn-primary:hover,
.puck-editor-container button[data-puck-button-primary]:hover,
.puck-editor-container [data-puck-actions] button:last-child:hover {
  background-color: #0056b3 !important;
  border-color: #004085 !important;
}

/* Disabled button states */
.puck-editor-container [data-puck-header] button:disabled,
.puck-editor-container [data-puck-header] .btn:disabled {
  opacity: 0.6 !important;
  cursor: not-allowed !important;
  background-color: var(--puck-color-grey-10, #efefef) !important;
  color: var(--puck-color-grey-06, #949494) !important;
}

/* Undo/Redo button group */
.puck-editor-container [data-puck-header] .puck-undo-redo {
  display: flex !important;
  gap: 2px !important;
}

.puck-editor-container [data-puck-header] .puck-undo-redo button {
  border-radius: 4px !important;
  min-width: 32px !important;
  padding: 6px 8px !important;
}

/* Ensure Publish button is visible */
.puck-header-actions button {
  display: inline-flex !important;
  align-items: center;
  justify-content: center;
  min-height: 32px;
  padding: 0.375rem 0.75rem;
  font-size: 0.875rem;
  line-height: 1.5;
  border-radius: 0.375rem;
  transition: all 0.15s ease-in-out;
}

/* Primary button (Publish) styling */
.puck-header-actions button[variant="primary"] {
  background-color: #007bff;
  border-color: #007bff;
  color: #fff;
}

.puck-header-actions button[variant="primary"]:hover:not(:disabled) {
  background-color: #0056b3;
  border-color: #004085;
}

.puck-header-actions button[variant="primary"]:disabled {
  opacity: 0.65;
  cursor: not-allowed;
}

/* Secondary button (Cancel) styling */
.puck-header-actions button[variant="outline-secondary"] {
  background-color: transparent;
  border-color: #6c757d;
  color: #6c757d;
}

.puck-header-actions button[variant="outline-secondary"]:hover:not(:disabled) {
  background-color: #6c757d;
  color: #fff;
}
