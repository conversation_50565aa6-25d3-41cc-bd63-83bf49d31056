{"ast": null, "code": "export { default } from \"./TablePaginationActions.js\";\nexport { default as tablePaginationActionsClasses } from \"./tablePaginationActionsClasses.js\";", "map": {"version": 3, "names": ["default", "tablePaginationActionsClasses"], "sources": ["C:/laragon/www/frontend/node_modules/@mui/material/esm/TablePaginationActions/index.js"], "sourcesContent": ["export { default } from \"./TablePaginationActions.js\";\nexport { default as tablePaginationActionsClasses } from \"./tablePaginationActionsClasses.js\";"], "mappings": "AAAA,SAASA,OAAO,QAAQ,6BAA6B;AACrD,SAASA,OAAO,IAAIC,6BAA6B,QAAQ,oCAAoC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}